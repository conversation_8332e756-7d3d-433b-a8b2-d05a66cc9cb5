package net.armcloud.paascenter.task.manager.executor.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ImageRequest;
import net.armcloud.paascenter.cms.model.request.InstanceVirtualRealSwitchRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpgradeImageTaskQueueBO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.rtc.PadMacLog;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.mapper.PadMacLogMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.task.manager.executor.impl.PadUpgradeImageTaskExecutorStrategy.buildPadOldParam;

/**
 * 升级真机镜像
 */
@Slf4j
@Component
public class PadVirtualRealSwitchTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final PadTaskMapper padTaskMapper;
    private final PadMacLogMapper padMacLogMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;
    private final ITaskService taskService;

    /**
     * 2种方式
     * 1. 启朔设备发送指令
     * 2. 凌点设备通过容器
     *
     * @param taskQueue 实例任务
     */
    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadTask padTask = padTaskMapper.getById(taskQueue.getSubTaskId());
        ImageRequest image = new ImageRequest();
        image.setId(padTask.getImageId());
        // 暂不需要放开此功能，暂为固定值
        image.setTag("latest");

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceVirtualRealSwitchRequest.Instance instance = new InstanceVirtualRealSwitchRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        instance.setImage(image);
        instance.setMac(getAndSetPadMac(padTask.getPadCode()));
        instance.setClearDiskData(padTask.getWipeData());
        InstanceVirtualRealSwitchRequest.ADI adi = new InstanceVirtualRealSwitchRequest.ADI();

        PadUpgradeImageTaskQueueBO padUpgradeImageTaskQueueBO = JSONUtil.toBean(taskQueue.getContentJson(), PadUpgradeImageTaskQueueBO.class);
        adi.setTemplateUrl(padUpgradeImageTaskQueueBO.getAdiUrl());
        adi.setTemplatePassword(padUpgradeImageTaskQueueBO.getAdiPassword());
        adi.setLayoutWidth(padUpgradeImageTaskQueueBO.getLayoutWidth());
        adi.setLayoutHigh(padUpgradeImageTaskQueueBO.getLayoutHigh());
        adi.setLayoutDpi(padUpgradeImageTaskQueueBO.getLayoutDpi());
        adi.setLayoutFps(padUpgradeImageTaskQueueBO.getLayoutFps());
        adi.setRealPhoneTemplateId(padUpgradeImageTaskQueueBO.getRealPhoneTemplateId());
        instance.setAdi(adi);
        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));

        InstanceVirtualRealSwitchRequest req = new InstanceVirtualRealSwitchRequest();
        req.setInstances(Collections.singletonList(instance));

        //记录task_rel_instance_detail表
        taskService.saveDeviceInstanceSingle(padTask.getTaskId(),padTask.getId(), TaskTypeAndChannelEnum.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getCbsTaskTypeEnum(),taskRelInstanceDetail,req);
        return req;
    }


    private String getAndSetPadMac(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        String mac = pad.getMac();
        if (StringUtils.isNotBlank(mac)) {
            return mac;
        }

        do {
            mac = MACUtils.generateMacAddress();
            int updateSize = padMapper.updateMacById(pad.getId(), mac);
            if (updateSize <= 0) {
                continue;
            }

            PadMacLog padMacLog = new PadMacLog();
            padMacLog.setPadCode(pad.getPadCode());
            padMacLog.setMac(mac);
            padMacLogMapper.insert(padMacLog);
            return mac;
        } while (true);
    }

    public PadVirtualRealSwitchTaskExecutorStrategy(PadMapper padMapper,PadTaskMapper padTaskMapper, PadMacLogMapper padMacLogMapper,InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                    ITaskService taskService) {
        this.padMapper = padMapper;
        this.padTaskMapper = padTaskMapper;
        this.padMacLogMapper = padMacLogMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        this.taskService = taskService;

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), "padVirtualRealSwitchTaskExecutorStrategy");
    }
}
