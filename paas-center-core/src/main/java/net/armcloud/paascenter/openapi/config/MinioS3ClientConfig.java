package net.armcloud.paascenter.openapi.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.*;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import net.armcloud.paascenter.cms.config.MinIOConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(MinIOConfig.class)
public class MinioS3ClientConfig {
    // @Bean
    // public S3Client s3Client(MinIOConfig properties) {
    //     // 禁用SSL验证
    //     System.setProperty("com.amazonaws.sdk.disableCertChecking", "true");
    //
    //     AwsBasicCredentials credentials = AwsBasicCredentials.create(properties.getAccessKey(), properties.getSecretKey());
    //     return S3Client.builder()
    //             .region(properties.getRegion())
    //             .credentialsProvider(StaticCredentialsProvider.create(credentials))
    //             .endpointOverride(URI.create(properties.getEndpoint()))
    //             .build();
    // }

    @Bean(name = "amazonS3Client")
    public AmazonS3 amazonS3Client (MinIOConfig properties) {
        //设置连接时的参数
        ClientConfiguration config = new ClientConfiguration();
        //设置连接方式为HTTP，可选参数为HTTP和HTTPS
        config.setProtocol(Protocol.HTTP);
        //设置网络访问超时时间
        config.setConnectionTimeout(5000);
        config.setUseExpectContinue(true);

        // 禁用SSL验证
        System.setProperty("com.amazonaws.sdk.disableCertChecking", "true");

        AWSCredentials credentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getSecretKey());
        //设置Endpoint
        AWSCredentialsProvider credentialsProvider = new InstanceProfileCredentialsProvider(false);
        AwsClientBuilder.EndpointConfiguration end_point = new AwsClientBuilder.EndpointConfiguration(properties.getEndpoint(), Regions.US_EAST_1.name());
        AmazonS3 amazonS3 = AmazonS3ClientBuilder.standard()
                .withClientConfiguration(config)
                .withCredentials(credentialsProvider)
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withEndpointConfiguration(end_point)
                .withPathStyleAccessEnabled(true).build();
        return amazonS3;
    }
}
