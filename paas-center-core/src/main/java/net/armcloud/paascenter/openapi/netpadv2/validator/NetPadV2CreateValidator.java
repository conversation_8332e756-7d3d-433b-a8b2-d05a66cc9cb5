package net.armcloud.paascenter.openapi.netpadv2.validator;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2BatchBootOnDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2BatchDelDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2BatchOffDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2CreateDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2QueryService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

/**
 * 网存实例V2校验器
 * 封装所有校验逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Component
public class NetPadV2CreateValidator {

    /**
     * 校验存储容量
     */
    public void validateStorageCapacity(Integer storageSize) {
        if (Objects.nonNull(storageSize)) {
            if (checkStorageCapacity(storageSize)) {
                throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_STORAGE_CAPACITY_NOT_EXIST_REQUIRED_MESSAGE);
            }
        }
    }

    private Boolean checkStorageCapacity(Integer storageCapacity) {
        List<Integer> list = new ArrayList<>(Arrays.asList(4, 16, 32, 64, 128, 256));
        return !list.contains(storageCapacity);
    }

    /**
     * 校验资源规格
     */
    public void validateResourceSpecification(ResourceSpecification resourceSpecification) {
        if (BeanUtil.isEmpty(resourceSpecification)) {
            throw new BasicException(SPECIFICATION_CODE_NOT_EXIST);
        }
    }

    /**
     * 校验网络存储资源
     */
    public void validateNetStorageRes(NetStorageRes storageRes) {
        if (Objects.isNull(storageRes) || storageRes.getStorageCapacity() <= 0) {
            throw new BasicException(NET_STORAGE_INSUFFICIENT_CAPACITY);
        }
    }

    /**
     * 校验镜像
     */
    public void validateCustomerUploadImage(CustomerUploadImage customerUploadImage) {
        if (BeanUtil.isEmpty(customerUploadImage)) {
            throw new BasicException(IMAGE_NOT_EXIST);
        }
    }

    /**
     * 校验ADI模板
     */
    public void validateRealPhoneTemplate(RealPhoneTemplate realPhoneTemplate) {
        if (Objects.isNull(realPhoneTemplate)) {
            throw new BasicException(REAL_PHONE_ADI_NOT_EXIST);
        }
    }

    /**
     * 校验屏幕布局
     */
    public void validateScreenLayout(ScreenLayout screenLayout) {
        if (BeanUtil.isEmpty(screenLayout)) {
            throw new BasicException(SCREEN_LAYOUT_NOT_EXIST);
        }
    }


    /**
     * 校验创建网存实例参数
     */
    public void validateCreateParams(NetPadV2CreateDTO dto) {
        if (dto == null) {
            throw new BasicException("请求参数不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (!StringUtils.hasText(dto.getClusterCode())) {
            throw new BasicException("集群编码不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (!StringUtils.hasText(dto.getSpecificationCode())) {
            throw new BasicException("规格代码不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (!StringUtils.hasText(dto.getImageId())) {
            throw new BasicException("镜像ID不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (dto.getNumber() == null || dto.getNumber() < 1 || dto.getNumber() > 100) {
            throw new BasicException("实例数量必须在1-100之间", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (dto.getStorageSize() == null || dto.getStorageSize() <= 0) {
            throw new BasicException("存储大小必须大于0", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        validateDns(dto.getDns());

        // 非随机ADI模板并且未传入ADI模板参数时，屏幕布局编码不能为空
        if (!dto.getRandomADITemplates() && dto.getRealPhoneTemplateId() == null && !StringUtils.hasText(dto.getScreenLayoutCode())) {
            throw new BasicException("屏幕布局编码不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }
    }

    /**
     * 校验批量开机参数
     */
    public void validateBatchBootOnParams(NetPadV2BatchBootOnDTO param) {
        if (param == null) {
            throw new BasicException("请求参数不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        validateDns(param.getDns());

        if (CollectionUtils.isEmpty(param.getPadCodes())) {
            throw new BasicException("实例编码列表不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getPadCodes().size() < 1 || param.getPadCodes().size() > 200) {
            throw new BasicException("实例数量范围1-200", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getTimeout() != null && (param.getTimeout() < 5 * 60 || param.getTimeout() > 120 * 60)) {
            throw new BasicException("超时时间必须在5分钟-120分钟之间", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }
    }

    private void validateDns(String dns) {
        // 校验dns格式是否正确
        if (StringUtils.hasText(dns)) {
            String[] dnsArr = dns.split(",");
            for (String dnsItem : dnsArr) {
                if (!dnsItem.matches("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$")) {
                    throw new BasicException("DNS格式错误", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
                }
            }
        }
    }

    /**
     * 校验批量关机参数
     */
    public void validateBatchOffParams(NetPadV2BatchOffDTO param) {
        if (param == null) {
            throw new BasicException("请求参数不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (CollectionUtils.isEmpty(param.getPadCodes())) {
            throw new BasicException("实例编码列表不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getPadCodes().size() < 1 || param.getPadCodes().size() > 200) {
            throw new BasicException("实例数量范围1-200", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getTimeout() != null && (param.getTimeout() < 5 * 60 || param.getTimeout() > 120 * 60)) {
            throw new BasicException("超时时间必须在5分钟-120分钟之间", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }
    }

    /**
     * 校验批量删除参数
     */
    public void validateBatchDeleteParams(NetPadV2BatchDelDTO param) {
        if (param == null) {
            throw new BasicException("请求参数不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (CollectionUtils.isEmpty(param.getPadCodes())) {
            throw new BasicException("实例编码列表不能为空", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getPadCodes().size() < 1 || param.getPadCodes().size() > 200) {
            throw new BasicException("实例数量范围1-200", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }

        if (param.getTimeout() != null && (param.getTimeout() < 5 * 60 || param.getTimeout() > 120 * 60)) {
            throw new BasicException("超时时间必须在5分钟-120分钟之间", BasicExceptionCode.PARAMETER_EXCEPTION.getStatus());
        }
    }

}
