package net.armcloud.paascenter.openapi.model.dto.netstorage;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/3/27 20:33
 * @Description:
 */
@Data
public class NetStorageNetWorkOffDTO {
    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;


    @NotNull(message = "实例编号不能为空")
    private String padCode;

    @ApiModelProperty(value = "网络存储Id Code")
    private String netStorageResUnitCode;

    /**
     * 国家编号
     */
    private String countryCode;

    /**
     * 安卓属性
     */
    private JSONObject androidProp;

}
