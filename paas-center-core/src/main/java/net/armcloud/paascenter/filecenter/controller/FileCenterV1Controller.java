package net.armcloud.paascenter.filecenter.controller;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.filecenter.model.dto.*;
import net.armcloud.paascenter.filecenter.service.UserFileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * 文件中心API控制器
 */
@Slf4j
@RestController
@RequestMapping("/file-center/open")
public class FileCenterV1Controller {

    @Autowired
    private UserFileService userFileService;

    /**
     * 检查URL格式是否正常
     * 
     * @param url 需要检查的URL字符串
     * @return true表示URL格式正常，false表示URL格式异常
     */
    private boolean checkUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        try {
            new URL(url);
            // 检查URL是否包含协议
            return url.startsWith("http://") || url.startsWith("https://");
        } catch (Exception e) {
            log.warn("Invalid URL format: {}, error: {}", url, e.getMessage());
            return false;
        }
    }

    @PostMapping("/file/cache")
    @ApiOperation(value = "缓存文件")
    public Result<Map<String, Object>> cacheFile(@RequestBody @Valid CacheFileDTO dto) {
        if (!checkUrl(dto.getFileUrl())) {
            throw new BasicException(100000, "URL格式不正确");
        }
        if (StringUtils.isBlank(dto.getFileName())) {
            throw new BasicException(100000, "文件名不能为空");
        }
        // check md5
        if (StringUtils.isBlank(dto.getFileMd5())) {
            throw new BasicException(100000, "MD5不能为空");
        }
        // check md5 32 位
        if (dto.getFileMd5().length() != 32) {
            throw new BasicException(100000, "MD5长度不正确");
        }
        dto.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        log.info("文件缓存接口入参:{}", JSONObject.toJSONString(dto));
        return userFileService.cacheFile(dto);
    }


    @PostMapping("/file/list")
    @ApiOperation(value = "文件列表")
    public Result<Page<Map<String, Object>>> list(@RequestBody @Valid FileListDTO param) {
        
        QueryFileListRequestDTO queryFileListRequestDTO = new QueryFileListRequestDTO();
        queryFileListRequestDTO.setFileName(param.getFileName());
        queryFileListRequestDTO.setPage(param.getPage());
        queryFileListRequestDTO.setRows(param.getRows());
        queryFileListRequestDTO.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        return Result.ok(userFileService.queryFileListForV1Api(queryFileListRequestDTO));
    }

    /**
     * 批量删除文件
     * @param param
     * @return
     */
    @PostMapping("/file/batch/del")
    public Result<?> batchDel(@RequestBody @Valid BatchDelDTO param) {
        if (CollectionUtils.isEmpty(param.getFileUniqueIds()) || param.getFileUniqueIds().size() > 200) {
            throw new BasicException(150001, "文件ID列表不能为空或单次请求文件数量不能超过200个");
        }
        List<String> deletedFileIds = userFileService.batchDeleteFilesByUniqueIds(param.getFileUniqueIds());

        // 全部删除成功
        if (param.getFileUniqueIds().size() == deletedFileIds.size()) {
            return Result.ok(true);
        } else {
            return Result.ok(deletedFileIds);
        }
    }


    @PostMapping("/app/cache")
    @ApiOperation(value = "缓存应用")
    public Result<List<Map<String, Object>>> cacheApp(@RequestBody @Valid CacheAppDTO param) {
        if (CollectionUtils.isEmpty(param.getApps())) {
            throw new BasicException(100000, "应用列表不能为空");
        }
        for (CacheAppDTO.App app : param.getApps()) {
            if (!checkUrl(app.getUrl())) {
                throw new BasicException(100000, "URL格式不正确");
            }
            // check md5 MD5 可以为空 如果不是空就校验长度
            if (StringUtils.isNotBlank(app.getMd5sum())) {
                if (app.getMd5sum().length() != 32) {
                    throw new BasicException(100000, "MD5长度不正确");
                }
            }
        }
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return userFileService.cacheApp(param);
    }

    @PostMapping("/app/detail")
    @ApiOperation(value = "应用详情")
    public Result<Map<String, Object>> detail(@RequestBody @Valid AppFileDetailDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(userFileService.appDetail(param));
    }

    @PostMapping("/app/list")
    @ApiOperation(value = "应用列表")
    public Result<Page<Map<String, Object>>> list(@RequestBody @Valid AppFileListDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(userFileService.appList(param));
    }

}