package net.armcloud.paascenter.filecenter.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.model.vo.FileDownloadCallbackVO;
import net.armcloud.paascenter.cms.config.MinIOConfig;
import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.filecenter.FileStorage;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.common.model.entity.filecenter.UserAppFile;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassifyRelation;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.MD5Utils;
import net.armcloud.paascenter.common.utils.UuidUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.feign.FileHandleClient;
import net.armcloud.paascenter.feign.dto.UploadEventDto;
import net.armcloud.paascenter.filecenter.config.FileCenterConfig;
import net.armcloud.paascenter.filecenter.config.OssConfig;
import net.armcloud.paascenter.filecenter.enums.FileTypeEnum;
import net.armcloud.paascenter.filecenter.mapper.FileStorageMapper;
import net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper;
import net.armcloud.paascenter.filecenter.mapper.UserAppFileMapper;
import net.armcloud.paascenter.filecenter.mapper.UserFileMapper;
import net.armcloud.paascenter.filecenter.model.dto.*;
import net.armcloud.paascenter.filecenter.model.vo.AppDetailsVO;
import net.armcloud.paascenter.filecenter.model.vo.FileDetailVO;
import net.armcloud.paascenter.filecenter.service.AppParseService;
import net.armcloud.paascenter.filecenter.service.FileStorageService;
import net.armcloud.paascenter.filecenter.service.OssService;
import net.armcloud.paascenter.filecenter.service.UserFileService;
import net.armcloud.paascenter.filecenter.utils.AliyunFCUtils;
import net.armcloud.paascenter.openapi.mapper.CustomerAppClassifyMapper;
import net.armcloud.paascenter.openapi.mapper.CustomerNewAppClassifyRelationMapper;
import net.armcloud.paascenter.task.service.impl.TaskService;
import net.armcloud.paascenter.task.utils.IdGenerateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.utils.http.RequestUtils.getCurrentRequest;

/**
 * User file service implementation
 */
@Slf4j
@Service
public class UserFileServiceImpl extends ServiceImpl<UserFileMapper, UserFile> implements UserFileService {

    private static final String FILE_STATUS_VALID = "valid";
    private static final String FILE_STATUS_INVALID = "invalid";
    private static final String FILE_STATUS_DEL = "del";
    private static final String UPLOAD_STATUS_SUCCESS = "success";
    private static final String UPLOAD_STATUS_UPLOADING = "uploading";
    private static final String UPLOAD_STATUS_FAILED = "failed";
    private static final String SOURCE_TYPE_UPLOAD = "upload";
    private static final String SOURCE_TYPE_URL = "url";
    private static final int MAX_APP_ID_GENERATION_ATTEMPTS = 10;

    @Value("${spring.profiles.active:unknown}")
    private String env;

    /**
     * 1走 aliyun oss 上传
     * 2走minio 上传
     */
    @Value("${upload.oss.type:1}")
    private Integer ossType;
    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private OssService ossService;

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private MinIOConfig minIOConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FileUploadTaskMapper fileUploadTaskMapper;

    @Autowired
    private AliyunFCUtils aliyunFCUtils;

    @Autowired
    private AppParseService appParseService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CustomerAppClassifyMapper customerAppClassifyMapper;

    @Autowired
    private CustomerNewAppClassifyRelationMapper customerNewAppClassifyRelationMapper;

    @Autowired
    private CustomerCallbackManager customerCallbackManager;

    @Autowired
    private CallbackTaskManager callbackTaskManager;

    @Autowired
    private FileCenterConfig fileCenterConfig;
    @Autowired
    private UserAppFileMapper userAppFileMapper;

    @Autowired
    private FileHandleClient  fileHandleClient;
    @Autowired
    private FileStorageMapper fileStorageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponseDTO initiateUpload(FileUploadRequestDTO request) {
        validateUploadRequest(request);
        
        Long fileStorageId = getUploadedFileStorageIdByFileMd5(request.getFileMd5());
        if (fileStorageId != null) {
            return createFastUploadResponse(fileStorageId);
        }

        FileTypeEnum fileType = checkFileType(request.getFileName());
        String objectKey;
        if(ossType==1){
            if(StringUtils.hasText(request.getDirectory())){
                objectKey = ossService.generateStoragePathByDirectory(request.getDirectory(),
                        request.getFileName(),
                        request.getFileMd5());
            }else{
                objectKey = ossService.generateStoragePath(
                        fileType.getCode(),
                        request.getFileName(),
                        request.getFileMd5());
            }

            String contentType = determineContentType(request.getFileName());
            Map<String, String> ossInfo = ossService.generatePresignedUploadUrl(objectKey, contentType, 86400L);

            FileStorage fileStorage = createFileStorage(request, objectKey);
            return createUploadResponse(fileStorage, ossInfo);
        }
        if(ossType==2){
            objectKey = ossService.generateStoragePath(
                    fileType.getCode(),
                    request.getFileName(),
                    request.getFileMd5());

            String endpoint = minIOConfig.getEndpoint();
            String bucket = minIOConfig.getBucket();
            String fileName = request.getFileName();

            String uploadUrl = null;
            // if (!objectKey.startsWith("/")) {
            //     uploadUrl = "/" + objectKey;
            // }else{
            //     uploadUrl = objectKey;
            // }
            uploadUrl = objectKey;
            String publicUrl = endpoint + "/" + bucket + "/" + uploadUrl;

            FileStorage fileStorage = fileStorageService.findByMd5(request.getFileMd5(), UPLOAD_STATUS_UPLOADING);
            if (fileStorage == null) {
                fileStorage = new FileStorage();
            }
            fileStorage.setFileMd5(request.getFileMd5());
            fileStorage.setFileSize(0L);
            fileStorage.setFileExt(getFileExtension(request.getFileName()));
            fileStorage.setStoragePath(request.getFileName());
            fileStorage.setPublicUrl(publicUrl);
            fileStorage.setPrivateUrl(uploadUrl);
            fileStorage.setUploadStatus(UPLOAD_STATUS_UPLOADING);
            fileStorage.setCreatedTime(new Date());
            fileStorageService.saveFileStorage(fileStorage);


            FileUploadResponseDTO response = new FileUploadResponseDTO();
            response.setFileStorageId(fileStorage.getId());
            response.setFileExists(false);
            response.setCallbackUrl(null);
            response.setRegion(null);
            response.setEndpoint(endpoint);
            response.setUploadUrl(uploadUrl);
            response.setPublicUrl(publicUrl);
            response.setAccessKeyId(null);
            response.setAccessKeySecret(null);
            response.setBucketName(bucket);
            response.setSecurityToken(null);
            response.setExpiration(null);
            return response;
        }
        return null;
    }

    private void validateUploadRequest(FileUploadRequestDTO request) {
        if (request.getFileMd5() == null && request.getFileName() == null) {
            throw new IllegalArgumentException("File MD5 or file name cannot be null");
        }
        if (request.getFileMd5() != null && request.getFileMd5().length() != 32) {
            throw new IllegalArgumentException("File MD5 must be 32 characters");
        }
    }

    private FileUploadResponseDTO createFastUploadResponse(Long fileStorageId) {
        FileUploadResponseDTO response = new FileUploadResponseDTO();
        response.setFileStorageId(fileStorageId);
        response.setFileExists(true);
        return response;
    }

    private FileStorage createFileStorage(FileUploadRequestDTO request, String objectKey) {
        FileStorage fileStorage = fileStorageService.findByMd5(request.getFileMd5(), UPLOAD_STATUS_UPLOADING);
        if (fileStorage == null) {
            fileStorage = new FileStorage();
        }
        
        fileStorage.setFileMd5(request.getFileMd5());
        fileStorage.setFileSize(0L);
        fileStorage.setFileExt(getFileExtension(request.getFileName()));
        fileStorage.setStoragePath("/" + objectKey);
        fileStorage.setPublicUrl(ossService.getPublicUrl(objectKey));
        fileStorage.setPrivateUrl(ossService.getPrivateUrl(objectKey));
        fileStorage.setUploadStatus(UPLOAD_STATUS_UPLOADING);
        fileStorage.setCreatedTime(new Date());
        
        return fileStorageService.saveFileStorage(fileStorage);
    }

    private FileUploadResponseDTO createUploadResponse(FileStorage fileStorage, Map<String, String> ossInfo) {
        FileUploadResponseDTO response = new FileUploadResponseDTO();
        response.setFileStorageId(fileStorage.getId());
        response.setFileExists(false);
        response.setCallbackUrl(ossConfig.getCallbackUrl());
        response.setRegion(ossConfig.getRegion());
        response.setEndpoint(ossConfig.getEndpoint());
        response.setUploadUrl(ossInfo.get("uploadUrl"));
        response.setPublicUrl(fileStorage.getPublicUrl());
        response.setAccessKeyId(ossInfo.get("accessKeyId"));
        response.setAccessKeySecret(ossInfo.get("accessKeySecret"));
        response.setBucketName(ossInfo.get("bucketName"));
        response.setSecurityToken(ossInfo.get("securityToken"));
        response.setExpiration(Long.parseLong(ossInfo.get("expiration")));
        return response;
    }

    private FileTypeEnum checkFileType(String fileName) {
        if (fileName == null) {
            return null;
        }
        String extension = getFileExtension(fileName);
        if (extension == null) {
            return FileTypeEnum.FILE;
        }
        // 如果是apk文件，则返回app
        if (extension.equals("apk")) {
            return FileTypeEnum.APP;
        }
        // 如果是iso文件，则返回img
        if (extension.equals("img")) {
            return FileTypeEnum.ISO;
        }
        // 如果是图片相关的文件，则返回image
        if (extension.equals("jpg") || extension.equals("jpeg") || extension.equals("png") || extension.equals("gif")
                || extension.equals("bmp")) {
            return FileTypeEnum.IMAGE;
        }
        return FileTypeEnum.FILE;
    }

    @Override
    public boolean isFastUpload(String fileMd5) {
        // Check if file already exists by MD5
        FileStorage existingFile = fileStorageService.findByMd5(fileMd5, "success");
        return existingFile != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchSaveFile(List<FileSaveDTO> request) {
        if (request == null || request.isEmpty()) {
            return null;
        }
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        UserFileMapper userFileMapper = getBaseMapper();
        List<String> fileUniqueIds = new ArrayList<>();

        // 校验sortNum参数
        List<FileSaveDTO> collect = request.stream().filter(item -> item.getSortNum() != null && item.getSortNum().intValue() > 10000)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            throw new BasicException("sorNum参数不能大于10000");
        }

        for (FileSaveDTO fileSaveDTO : request) {
            // 更新文件存储的上传状态
            FileStorage fileStorage = fileStorageService.findByStorageId(fileSaveDTO.getFileStorageId());
            if (fileStorage == null) {
                continue;
            }
            // 如果是minio则更新FileStorage的upload_status为success
            if(ossType==2){
                LambdaUpdateWrapper<FileStorage> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(FileStorage::getUploadStatus, "success");
                updateWrapper.set(FileStorage::getFileSize, fileSaveDTO.getFileSize());
                updateWrapper.eq(FileStorage::getId, fileStorage.getId());
                fileStorageMapper.update(updateWrapper);
            }

            UserFile userFile = new UserFile();
            userFile.setFileStorageId(fileStorage.getId());
            // 生成文件唯一标识
            if (fileSaveDTO.getFileType().equals(FileUploadTask.FILE_TYPE_APP)) {
                userFile.setFileUniqueId(String.valueOf(tryGenerateAppId(customerId)));
            } else {
                userFile.setFileUniqueId(UuidUtils.generateUUIDWithoutHyphens());
            }
            Date nowDate = new Date();
            userFile.setCustomerId(customerId);
            userFile.setFileType(fileSaveDTO.getFileType());
            userFile.setFileName(fileSaveDTO.getFileName());
            userFile.setFileStatus(FILE_STATUS_VALID);
            userFile.setCreatedTime(nowDate);
            userFile.setCreatedBy(customerId);
            userFile.setUpdatedTime(nowDate);
            userFile.setUpdatedBy(customerId);
            userFile.setSourceType(SOURCE_TYPE_UPLOAD);
            userFile.setSortNum(fileSaveDTO.getSortNum());
            userFile.setFileComment(fileSaveDTO.getDescription());
            userFileMapper.insert(userFile);

            fileUniqueIds.add(userFile.getFileUniqueId());

            if (StringUtils.hasText(fileSaveDTO.getCustomerAppClassifyId())
                    && fileSaveDTO.getFileType().equals(FileUploadTask.FILE_TYPE_APP)) {

                CustomerNewAppClassifyRelation customerNewAppClassifyRelation = new CustomerNewAppClassifyRelation();
                customerNewAppClassifyRelation.setAppId(Long.parseLong(userFile.getFileUniqueId()));
                customerNewAppClassifyRelation.setFileId(userFile.getId());
                customerNewAppClassifyRelation.setCustomerId(customerId);
                customerNewAppClassifyRelation
                        .setNewAppClassifyId(Long.valueOf(fileSaveDTO.getCustomerAppClassifyId()));
                customerNewAppClassifyRelation.setCreateBy(String.valueOf(customerId));
                customerNewAppClassifyRelation.setCreateTime(new Date());
                customerNewAppClassifyRelation.setUpdateBy(String.valueOf(customerId));
                customerNewAppClassifyRelation.setUpdateTime(new Date());

                customerNewAppClassifyRelationMapper.insert(customerNewAppClassifyRelation);
            }
        }
        return fileUniqueIds;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null) {
            return null;
        }
        // 如果文件名没有点，则返回bin
        if (!fileName.contains(".")) {
            return "bin";
        }
        // 处理复合后缀情况
        String lowerFileName = fileName.toLowerCase();
        // 定义已知的复合后缀列表
        String[] compoundExtensions = { ".tar.gz", ".tar.bz2", ".tar.xz" };
        // 检查文件名是否以任何复合后缀结尾
        for (String ext : compoundExtensions) {
            if (lowerFileName.endsWith(ext)) {
                return ext.substring(1); // 去掉前导点号
            }
        }

        // 处理普通后缀情况
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * Determine content type based on file name extension
     */
    private String determineContentType(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "application/octet-stream";
        }

        String extension = getFileExtension(fileName);
        if (extension == null) {
            return "application/octet-stream";
        }

        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "txt":
                return "text/plain";
            case "zip":
                return "application/zip";
            case "rar":
                return "application/x-rar-compressed";
            case "tar":
                return "application/x-tar";
            case "gz":
            case "gzip":
                return "application/gzip";
            case "apk":
                return "application/vnd.android.package-archive";
            case "iso":
                return "application/x-iso9660-image";
            default:
                return "application/octet-stream";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchDeleteFiles(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return Collections.emptyList();
        }
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());

        List<String> deletedFileIds = new ArrayList<>();

        // 删除用户文件记录
        LambdaQueryWrapper<UserFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserFile::getId, fileIds);
        // 非管理员只能删除自己的文件
        if (!redisService.isAdmin(customerId)) {
            queryWrapper.eq(UserFile::getCustomerId, customerId);
        }

        List<UserFile> userFiles = list(queryWrapper);
        for (UserFile userFile : userFiles) {
            if (userFile.getFileType().equals("app")) {
                // 删除应用分类关联
                customerNewAppClassifyRelationMapper.delete(new LambdaQueryWrapper<CustomerNewAppClassifyRelation>()
                        .eq(CustomerNewAppClassifyRelation::getAppId, Long.parseLong(userFile.getFileUniqueId()))
                        .eq(CustomerNewAppClassifyRelation::getCustomerId, userFile.getCustomerId()));
            }
            deletedFileIds.add(String.valueOf(userFile.getId()));
        }
        update(new LambdaUpdateWrapper<UserFile>().set(UserFile::getFileStatus, FILE_STATUS_DEL)
                .in(UserFile::getId, fileIds));

        return deletedFileIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchDeleteFilesByUniqueIds(List<String> fileUniqueIds) {
        if (fileUniqueIds == null || fileUniqueIds.isEmpty()) {
            return Collections.emptyList();
        }
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());

        List<String> deletedFileIds = new ArrayList<>();

        // 删除用户文件记录
        LambdaQueryWrapper<UserFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserFile::getFileUniqueId, fileUniqueIds);
        queryWrapper.eq(UserFile::getFileType, FileUploadTask.FILE_TYPE_FILE);
        queryWrapper.eq(UserFile::getCustomerId, customerId);

        List<UserFile> userFiles = list(queryWrapper);
        for (UserFile userFile : userFiles) {
            deletedFileIds.add(userFile.getFileUniqueId());
        }
        
        update(new LambdaUpdateWrapper<UserFile>().set(UserFile::getFileStatus, FILE_STATUS_DEL)
                .in(UserFile::getFileUniqueId, fileUniqueIds)
                .eq(UserFile::getFileType, FileUploadTask.FILE_TYPE_FILE)
                .eq(UserFile::getCustomerId, customerId));

        return deletedFileIds;
    }

    @Override
    public Long getUploadedFileStorageIdByFileMd5(String fileMd5) {
        if (!StringUtils.hasText(fileMd5)) {
            return null;
        }

        // 通过 FileStorageService 查找文件, 并且上传状态为success
        FileStorage fileStorage = fileStorageService.findByMd5(fileMd5, "success");
        if (fileStorage == null) {
            return null;
        }

        // 如果找到文件，返回其 ID，否则返回 null
        return fileStorage.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFileStatus(FileStatusUpdateDTO request) {
        FileStorage fileStorage = fileStorageService.findByStorageId(request.getFileStorageId());
        if (fileStorage != null) {
            fileStorage.setFileSize(request.getFileSize());
            fileStorage.setUploadStatus(request.getUploadStatus());
            fileStorageService.saveFileStorage(fileStorage);
        }
        return true;
    }

    @Override
    public Page<Map<String, Object>> queryFileListForV1Api(QueryFileListRequestDTO request) {
        PageHelper.startPage(request.getPage(), request.getRows());
        request.setFileType("file");
        Page<Map<String, Object>> page = null;
        List<Map<String, Object>> result = new ArrayList<>();
        List<FileDetailVO> fileDetailVOs = this.baseMapper.queryFileDetails(request);
        for (FileDetailVO fileDetailVO : fileDetailVOs) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", fileDetailVO.getId());
            map.put("fileUniqueId", fileDetailVO.getFileUniqueId());
            map.put("fileName", fileDetailVO.getFileName());
            map.put("fileSize", fileDetailVO.getFileSize());
            map.put("fileMd5", fileDetailVO.getFileMd5());
            map.put("originUrl", fileDetailVO.getOriginalUrl());
            map.put("createdTime", fileDetailVO.getCreatedTime().getTime());
            result.add(map);
        }
        page = new Page<>(result);
        page.updatePageInfo(fileDetailVOs);
        return page;
    }

    @Override
    public Page<Map<String, Object>> queryFileList(QueryFileListRequestDTO request) {
        PageHelper.startPage(request.getPage(), request.getRows());
        Page<Map<String, Object>> page = null;
        List<Map<String, Object>> result = new ArrayList<>();
        if ("file".equals(request.getFileType())) {
            List<FileDetailVO> fileDetailVOs = this.baseMapper.queryFileDetails(request);
            for (FileDetailVO fileDetailVO : fileDetailVOs) {
                Map<String, Object> map = new HashMap<>();
                map.put("fileId", fileDetailVO.getId());
                map.put("fileStorageId", fileDetailVO.getFileStorageId());
                map.put("fileUniqueId", fileDetailVO.getFileUniqueId());
                map.put("customerId", fileDetailVO.getCustomerId());
                map.put("fileName", fileDetailVO.getFileName());
                map.put("fileType", fileDetailVO.getFileType());
                map.put("fileSize", fileDetailVO.getFileSize());
                map.put("fileMd5", fileDetailVO.getFileMd5());
                map.put("publicUrl", fileDetailVO.getPublicUrl());
                map.put("createdTime",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(fileDetailVO.getCreatedTime()));
                map.put("customerName", fileDetailVO.getCustomerName());
                map.put("customerAccount", fileDetailVO.getCustomerAccount());
                map.put("description", fileDetailVO.getFileComment());
                result.add(map);
            }
            page = new Page<>(result);
            page.updatePageInfo(fileDetailVOs);
        } else {
            List<AppDetailsVO> appDetailsVOs = this.baseMapper.queryAppFiles(request);
            for (AppDetailsVO appDetailsVO : appDetailsVOs) {
                Map<String, Object> map = new HashMap<>();
                map.put("appFileId", appDetailsVO.getAppFileId());
                map.put("fileId", appDetailsVO.getId());
                map.put("fileStorageId", appDetailsVO.getFileStorageId());
                map.put("appId", Integer.parseInt(appDetailsVO.getFileUniqueId()));
                map.put("customerId", appDetailsVO.getCustomerId());
                map.put("fileName", appDetailsVO.getFileName());
                map.put("fileType", appDetailsVO.getFileType());
                map.put("fileSize", appDetailsVO.getFileSize());
                map.put("fileMd5", appDetailsVO.getFileMd5());
                map.put("publicUrl", appDetailsVO.getPublicUrl());
                map.put("createdTime",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(appDetailsVO.getCreatedTime()));
                map.put("appName", appDetailsVO.getAppName());
                map.put("packageName", appDetailsVO.getPackageName());
                map.put("versionName", appDetailsVO.getVersionName());
                map.put("versionCode", appDetailsVO.getVersionCode());
                map.put("minSdkVersion", appDetailsVO.getMinSdkVersion());
                map.put("targetSdkVersion", appDetailsVO.getTargetSdkVersion());
                map.put("installationRequirements", appDetailsVO.getInstallationRequirements());
                map.put("isSystemApp", appDetailsVO.getIsSystemApp());
                map.put("isDebugApp", appDetailsVO.getIsDebugApp());
                map.put("developerName", appDetailsVO.getDeveloperName());
                map.put("signatureHash", appDetailsVO.getSignatureHash());
                map.put("iconPublicUrl", appDetailsVO.getIconPublicUrl());
                map.put("supportedAbis", appDetailsVO.getSupportedAbis());
                map.put("installLocation", appDetailsVO.getInstallLocation());
                map.put("appSize", appDetailsVO.getAppSize());
                map.put("mainActivity", appDetailsVO.getMainActivity());
                map.put("permissions", appDetailsVO.getPermissions());
                map.put("description", appDetailsVO.getFileComment());
                map.put("customerName", appDetailsVO.getCustomerName());
                map.put("customerAccount", appDetailsVO.getCustomerAccount());
                map.put("sortNum", appDetailsVO.getSortNum());

                QueryNewAppClassifyNameDTO queryNewAppClassifyNameDTO = new QueryNewAppClassifyNameDTO();
                if (request.getCustomerId() != null) {
                    queryNewAppClassifyNameDTO.setCustomerId(request.getCustomerId());
                }
                queryNewAppClassifyNameDTO.setAppIds(Arrays.asList(Long.parseLong(appDetailsVO.getFileUniqueId())));
                List<NewAppClassifyNameVO> newAppClassifyList = customerNewAppClassifyRelationMapper
                        .selectAppListByAppIds(queryNewAppClassifyNameDTO);
                if (newAppClassifyList != null && !newAppClassifyList.isEmpty()) {
                    // 多个逗号隔开
                    map.put("appClassifyName", String.join(",", newAppClassifyList.stream()
                            .map(NewAppClassifyNameVO::getNewAppClassifyName).collect(Collectors.toList())));
                } else {
                    map.put("appClassifyName", "");
                }

                UserAppFile userAppFile = userAppFileMapper.getByAppFileIdAndCustomerId(
                        Long.parseLong(appDetailsVO.getFileUniqueId()), CustomerUtils.getAndVerifyUserId(getCurrentRequest()));
                if (null != userAppFile) {
                    if (null != userAppFile.getAppName()) {
                        map.put("appName", userAppFile.getAppName());
                    }
                    if (null != userAppFile.getIconPublicUrl()) {
                        map.put("iconPublicUrl", userAppFile.getIconPublicUrl());
                    }
                }
                result.add(map);
            }
            page = new Page<>(result);
            page.updatePageInfo(appDetailsVOs);
        }
        // 执行查询
        return page;
    }

    /**
     * 创建文件上传任务
     * @param customerId 客户ID
     * @param fileType 文件类型
     * @param fileStorageId 文件存储ID
     * @param fileId 文件ID
     * @param originalUrl 原始URL
     * @param fileMd5 文件MD5
     * @param fileExtInfo 文件扩展信息
     * @return 文件上传任务
     */
    private FileUploadTask createFileUploadTask(Long customerId, String fileType, Long fileStorageId,
                                                Long fileId, String originalUrl, String fileMd5, String fileExtInfo) {
        // 创建主任务
        Task masterTask = taskService.addTaskService(TaskTypeConstants.FILE_UPLOAD.getType(),
                TaskStatusConstants.EXECUTING.getStatus(), customerId, null, null);
        int maxRetry = 3;
        int retry = 0;
        while (retry < maxRetry) {
            try {
                // 创建上传任务
                FileUploadTask task = new FileUploadTask();
                task.setTaskId(masterTask.getId());
                task.setOriginalUrl(originalUrl);
                task.setCustomerId(customerId);
                task.setFileType(fileType);
                task.setStatus(TaskStatusConstants.EXECUTING.getStatus());
                task.setFileMd5(fileMd5);
                task.setFileSize(0L);
                task.setFileExtInfo(fileExtInfo);
                task.setCreatedTime(new Date());
                task.setUpdatedTime(new Date());
                task.setFileStorageId(fileStorageId);
                task.setFileId(fileId);
                task.setUniqueId(IdGenerateUtils.generateUniqueIdApprox());
                fileUploadTaskMapper.insert(task);
                return task;
            } catch (DuplicateKeyException e) {
                // 主键或唯一键冲突（如 unique_id 冲突）
                log.warn("文件上传任务唯一ID重复，正在重试，次数: {}", retry + 1, e);
                retry++;
            } catch (Exception e) {
                log.error("创建文件上传任务失败", e);
                throw e;
            }
        }
        throw new BasicException("创建文件上传任务失败，已达最大重试次数");
    }
    /**
     * 创建用户文件记录
     * @param fileStorageId 文件存储ID
     * @param customerId 客户ID
     * @param fileType 文件类型
     * @param fileName 文件名
     * @param fileUniqueId 文件唯一ID
     * @param originalUrl 原始URL
     * @param fileStatus 文件状态
     * @return 用户文件记录
     */
    private UserFile createUserFileRecord(Long fileStorageId, Long customerId, String fileType, 
            String fileName, String fileUniqueId, String originalUrl, String fileStatus, String fileComment) {
        UserFile userFile = new UserFile();
        userFile.setFileStorageId(fileStorageId);
        userFile.setFileUniqueId(fileUniqueId);
        userFile.setCustomerId(customerId);
        userFile.setFileType(fileType);
        userFile.setFileName(fileName);
        userFile.setFileStatus(fileStatus);
        userFile.setSourceType(SOURCE_TYPE_URL);
        userFile.setOriginalUrl(originalUrl);
        userFile.setCreatedTime(new Date());
        userFile.setCreatedBy(customerId);
        userFile.setUpdatedTime(new Date());
        userFile.setUpdatedBy(customerId);
        userFile.setFileComment(fileComment);
        this.baseMapper.insert(userFile);
        return userFile;
    }

    @Value("${paas.filecenter.aliyun_fc_name:func-efbgebi1}")
    private String aliyunFcFunctionName;
    @Value("${paas.filecenter.aliyun_fc_callback_url:http://openapi-test.armcloud.net:18010/openapi/filecenter/aliyunfc/callback1}")
    private String aliyunFcCallbackUrl;
    /**
     * 调用阿里云函数
     * @param task 上传任务
     * @param storagePath 文件存储
     */
    private String invokeAliyunFunction(FileUploadTask task, String storagePath, int delaySeconds) {
        Map<String, Object> body = new HashMap<>();
        body.put("uploadTaskId", task.getId());
        body.put("originalUrl", task.getOriginalUrl());
        body.put("storagePath", storagePath);
        body.put("fileType", task.getFileType());
        body.put("fileExtInfo", task.getFileExtInfo());
        body.put("callbackUrl", aliyunFcCallbackUrl);


        if(ossType == BigDecimal.ONE.intValue()){
            return aliyunFCUtils.asyncInvokeFunction(aliyunFcFunctionName, JSON.toJSONString(body), delaySeconds);
        }else{
            /**
             * 私有云处理 调用文件处理服务同等余 公有云函数调用
             */
            CompletableFuture.runAsync(() -> {
                try {
                    UploadEventDto dto = new UploadEventDto();
                    dto.setUploadTaskId(task.getId());
                    dto.setCallbackUrl(aliyunFcCallbackUrl);
                    dto.setOriginalUrl(task.getOriginalUrl());
                    dto.setStorageKey(storagePath);
                    dto.setFileExtInfo(task.getFileExtInfo());
                    dto.setFileType(task.getFileType());

                    log.info("异步调用私有云文件处理服务: {}", JSON.toJSONString(dto));
                    fileHandleClient.handle(dto);
                } catch (Exception e) {
                    log.error("异步调用文件处理服务失败: {}", e.getMessage(), e);
                }
            });
            return null;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<String, Object>> cacheFile(CacheFileDTO request) {
        if (isUploadTaskExists(request.getFileUrl(), request.getCustomerId())) {
            return Result.fail(500, "重复的文件上传任务");
        }

        // 查询当前用户尚未超时且正在上传、等待上传的文件数量
        long uploadFileCount = fileUploadTaskMapper.selectCount(new LambdaQueryWrapper<FileUploadTask>()
                .eq(FileUploadTask::getCustomerId, request.getCustomerId())
                .gt(FileUploadTask::getCreatedTime, new Date(System.currentTimeMillis() - fileCenterConfig.getFileUploadTaskTimeout() * 1000))
                .in(FileUploadTask::getStatus, Arrays.asList(TaskStatusConstants.EXECUTING.getStatus(), TaskStatusConstants.WAIT_EXECUTE.getStatus())));
        
        if (uploadFileCount + 1 > fileCenterConfig.getMaxUploadFileCount()) {
            return Result.fail(500, "当前用户同时上传的文件、应用数量已达上限 [" + fileCenterConfig.getMaxUploadFileCount() + "] 个");
        }

        // 检查文件是否存在
        // boolean isFileExists = false;
        // FileStorage fileStorage = null;
        // if (StringUtils.hasText(request.getFileMd5())) {
        //     fileStorage = fileStorageService.findByMd5(request.getFileMd5(), UPLOAD_STATUS_SUCCESS);
        //     if (fileStorage == null) {
        //         fileStorage = fileStorageService.findByMd5(request.getFileMd5(), UPLOAD_STATUS_UPLOADING);
        //     } else {
        //         isFileExists = true;
        //     }
        // }

        // // 创建文件存储记录
        // if (fileStorage == null) {
        //     String urlMd5 = MD5Utils.generateMD5(request.getFileUrl());
        //     String objectKey = ossService.generateStoragePath(FileUploadTask.FILE_TYPE_FILE, request.getFileName(), urlMd5);
        //     fileStorage = createFileStorageRecord(request.getFileMd5(), request.getFileName(), FileUploadTask.FILE_TYPE_FILE, objectKey);
        // }

        // 创建用户文件记录
        String fileUniqueId = UuidUtils.generateUUIDWithoutHyphens();
        UserFile userFile = createUserFileRecord(0L, request.getCustomerId(), 
                FileUploadTask.FILE_TYPE_FILE, request.getFileName(), fileUniqueId, 
                request.getFileUrl(), FILE_STATUS_INVALID, null);

                // 调用阿里云函数
        
        // 创建上传任务
        FileUploadTask task = createFileUploadTask(request.getCustomerId(), FileUploadTask.FILE_TYPE_FILE,
                0L, userFile.getId(), request.getFileUrl(), request.getFileMd5(), null);

        String storagePath = ossService.generateStoragePath(FileUploadTask.FILE_TYPE_FILE, request.getFileName(), request.getFileMd5());

        String requestId = invokeAliyunFunction(task, storagePath, 1);

        task.setAliyunFcRequestId(requestId);

        fileUploadTaskMapper.updateById(task);


        Map<String, Object> result = new HashMap<>();
        result.put("taskId", task.getTaskId());
        result.put("fileUniqueId", fileUniqueId);

        return Result.ok(result);
    }

    /**
     * 从URL中获取文件名
     * @param url URL
     * @return 文件名
     */
    private String getFilenameInUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return null;
        }
        
        try {
            // 移除URL中的查询参数
            String cleanUrl = url;
            if (url.contains("?")) {
                cleanUrl = url.substring(0, url.indexOf("?"));
            }
            
            // 尝试从路径中获取文件名
            String fileName = cleanUrl.substring(cleanUrl.lastIndexOf("/") + 1);
            
            // 检查文件名是否为空或者只包含特殊字符
            if (!StringUtils.hasText(fileName) || fileName.matches("^[\\W_]+$")) {
                // 尝试从Content-Disposition头获取文件名（这里无法实现，但实际应用中可能需要）
                // 如果没有有效的文件名，生成一个随机文件名
                return "file_" + System.currentTimeMillis();
            }
            
            // 处理URL编码的文件名
            if (fileName.contains("%")) {
                try {
                    fileName = java.net.URLDecoder.decode(fileName, "UTF-8");
                } catch (Exception e) {
                    // 解码失败，使用原始文件名
                }
            }
            
            return fileName;
        } catch (Exception e) {
            // 如果解析失败，返回一个默认文件名
            return "unknown_file_" + System.currentTimeMillis();
        }
    }


    private boolean isUploadTaskExists(String originalUrl, Long customerId) {
        // 检查是否存在重复任务,
        // 检测逻辑是：
        // 1. 默认是创建时间在1个小时以内的
        // 2. 如果存在，则返回失败
        // 3. 如果不存在，则创建任务
        return fileUploadTaskMapper.exists(new LambdaQueryWrapper<FileUploadTask>()
                .eq(FileUploadTask::getOriginalUrl, originalUrl)
                .eq(FileUploadTask::getCustomerId, customerId)
                .in(FileUploadTask::getStatus, Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus()))
                .gt(FileUploadTask::getCreatedTime, new Date(System.currentTimeMillis() - fileCenterConfig.getFileUploadTaskTimeout() * 1000)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Map<String, Object>>> cacheApp(CacheAppDTO request) {

        // 查询没有超时的上传任务数量
        long uploadFileCount = fileUploadTaskMapper.selectCount(new LambdaQueryWrapper<FileUploadTask>()
                .eq(FileUploadTask::getCustomerId, request.getCustomerId())
                .gt(FileUploadTask::getCreatedTime, new Date(System.currentTimeMillis() - fileCenterConfig.getFileUploadTaskTimeout() * 1000))
                .in(FileUploadTask::getStatus, Arrays.asList(TaskStatusConstants.EXECUTING.getStatus(), TaskStatusConstants.WAIT_EXECUTE.getStatus())));
        
        if (uploadFileCount + request.getApps().size() > fileCenterConfig.getMaxUploadFileCount()) {
            return Result.fail(500, "当前用户同时上传的文件、应用数量已达上限 [" + fileCenterConfig.getMaxUploadFileCount() + "] 个");
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        int delaySeconds = request.getApps().size();
        for (CacheAppDTO.App app : request.getApps()) {
            if (!StringUtils.hasText(app.getUrl())) {
                continue;
            }
            // 检查是否存在重复任务
            if (isUploadTaskExists(app.getUrl(), request.getCustomerId())) {
                continue;
            }

            // 检查应用是否已存在
            if (app.getAppId() != null) {
                UserFile existingFile = this.baseMapper.selectOne(new LambdaQueryWrapper<UserFile>()
                        .eq(UserFile::getCustomerId, request.getCustomerId())
                        .eq(UserFile::getFileUniqueId, app.getAppId() + ""));
                if (existingFile != null) {
                    continue;
                }
            }

            // 检查文件是否存在
            // boolean isFileExists = false;
            // FileStorage fileStorage = null;
            // if (StringUtils.hasText(app.getMd5sum())) {
            //     fileStorage = fileStorageService.findByMd5(app.getMd5sum(), UPLOAD_STATUS_SUCCESS);
            //     if (fileStorage == null) {
            //         fileStorage = fileStorageService.findByMd5(app.getMd5sum(), UPLOAD_STATUS_UPLOADING);
            //     } else {
            //         isFileExists = true;
            //     }
            // }

            // 创建文件存储记录
            // if (fileStorage == null) {
            //     String urlMd5 = MD5Utils.generateMD5(app.getUrl());
            //     String objectKey = ossService.generateStoragePath(FileUploadTask.FILE_TYPE_APP, urlMd5 + ".apk", urlMd5);
            //     fileStorage = createFileStorageRecord(app.getMd5sum(), app.getAppName(), FileUploadTask.FILE_TYPE_APP, objectKey);
            // }

            String fileName = getFilenameInUrl(app.getUrl());
            String fileExt = getFileExtension(fileName);
            String md5Sum = app.getMd5sum();
            if (null == md5Sum) {
                md5Sum = MD5Utils.generateMD5(app.getUrl());
            }
            if (null == fileExt) {
                fileExt = "apk";
            }
            String storagePath = ossService.generateStoragePath(FileUploadTask.FILE_TYPE_APP, md5Sum + "." + fileExt, md5Sum);

            // 创建用户文件记录
            String fileUniqueId = app.getAppId() != null ? app.getAppId().toString() : 
                    String.valueOf(tryGenerateAppId(request.getCustomerId()));
            UserFile userFile = createUserFileRecord(0L, request.getCustomerId(),
                    FileUploadTask.FILE_TYPE_APP, fileName, fileUniqueId, app.getUrl(),
                    FILE_STATUS_INVALID, app.getDescription());

            // 创建上传任务
            JSONObject fileExtInfo = new JSONObject();
            fileExtInfo.putAll(JSON.parseObject(JSON.toJSONString(app)));
            fileExtInfo.put("parse", request.getParse());
            fileExtInfo.put("skipCheck", request.getSkipCheck());

            FileUploadTask task = createFileUploadTask(request.getCustomerId(), FileUploadTask.FILE_TYPE_APP, 0L, userFile.getId(), app.getUrl(), md5Sum, fileExtInfo.toJSONString());

            if (delaySeconds < 1) {
                delaySeconds = 1;
            }
            // 调用阿里云函数
            String requestId = invokeAliyunFunction(task, storagePath, delaySeconds);
            // 延迟时间减1
            delaySeconds -= 1;

            task.setAliyunFcRequestId(requestId);

            fileUploadTaskMapper.updateById(task);

            Map<String, Object> result = new HashMap<>();
            result.put("url", app.getUrl());
            result.put("taskId", task.getTaskId());
            result.put("appId", Integer.parseInt(fileUniqueId));
            resultList.add(result);
        }
        if (resultList.size() == 0) {
            return Result.fail(500, "重复的应用上传任务");
        }
        return Result.ok(resultList);
    }

    @Override
    public Map<String, Object> appDetail(AppFileDetailDTO request) {
        UserFile userFile = this.baseMapper.selectOne(new LambdaQueryWrapper<UserFile>()
                .eq(UserFile::getCustomerId, request.getCustomerId())
                .eq(UserFile::getFileUniqueId, request.getAppId() + ""));
        if (userFile == null) {
            return null;
        }
        AppDetailsVO appDetailsVO = this.baseMapper.queryAppDetailByAppId(request.getAppId().toString(),
                request.getCustomerId());
        if (appDetailsVO == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        result.put("appId", Integer.parseInt(appDetailsVO.getFileUniqueId()));
        result.put("originalUrl", appDetailsVO.getOriginalUrl());
        result.put("appName", appDetailsVO.getAppName());
        result.put("packageName", appDetailsVO.getPackageName());
        result.put("versionName", appDetailsVO.getVersionName());
        result.put("versionNo", appDetailsVO.getVersionCode());
        result.put("minSdkVersion", appDetailsVO.getMinSdkVersion());
        result.put("targetSdkVersion", appDetailsVO.getTargetSdkVersion());
        result.put("description", appDetailsVO.getFileComment());
        result.put("signMd5", appDetailsVO.getSignatureHash());
        result.put("createTime", appDetailsVO.getCreatedTime().getTime());

        UserAppFile userAppFile = userAppFileMapper.getByAppFileIdAndCustomerId(
                Long.parseLong(appDetailsVO.getFileUniqueId()), CustomerUtils.getAndVerifyUserId(getCurrentRequest()));
        if (null != userAppFile) {
            if (null != userAppFile.getAppName()) {
                result.put("appName", userAppFile.getAppName());
            }
        }

        return result;
    }

    @Override
    public Page<Map<String, Object>> appList(AppFileListDTO request) {
        PageHelper.startPage(request.getPage(), request.getRows());
        List<AppDetailsVO> appDetailsVOs = this.baseMapper.queryAppDetails(request);

        Page<Map<String, Object>> page = null;
        List<Map<String, Object>> result = new ArrayList<>();
        for (AppDetailsVO appDetailsVO : appDetailsVOs) {
            Map<String, Object> map = new HashMap<>();
            map.put("fileId", appDetailsVO.getId());
            map.put("appId", Integer.parseInt(appDetailsVO.getFileUniqueId()));
            map.put("originUrl", appDetailsVO.getOriginalUrl());
            map.put("description", appDetailsVO.getFileComment());
            map.put("packageName", appDetailsVO.getPackageName());
            map.put("appName", appDetailsVO.getAppName());
            map.put("versionName", appDetailsVO.getVersionName());
            map.put("versionNo", appDetailsVO.getVersionCode());
            map.put("signMd5", appDetailsVO.getSignatureHash());
            map.put("available", true);
            map.put("createTime", appDetailsVO.getCreatedTime().getTime());
            map.put("iconPath", appDetailsVO.getIconPublicUrl());

            UserAppFile userAppFile = userAppFileMapper.getByAppFileIdAndCustomerId(
                    Long.parseLong(appDetailsVO.getFileUniqueId()), CustomerUtils.getAndVerifyUserId(getCurrentRequest()));
            if (null != userAppFile) {
                if (null != userAppFile.getAppName()) {
                    map.put("appName", userAppFile.getAppName());
                }
                if (null != userAppFile.getIconPublicUrl()) {
                    map.put("iconPath", userAppFile.getIconPublicUrl());
                }
            }

            List<AppClassifyNameVO> classifyNameList = customerAppClassifyMapper
                    .getClassifyNameByAppIds(request.getCustomerId(), Arrays.asList(appDetailsVO.getPackageName()));
            List<Map<String, Object>> classifyNameResultList = new ArrayList<>();
            for (AppClassifyNameVO classifyName : classifyNameList) {
                Map<String, Object> classifyNameMap = new HashMap<>();
                classifyNameMap.put("classifyName", classifyName.getClassifyName());
                classifyNameMap.put("classifyType", classifyName.getClassifyType());
                classifyNameResultList.add(classifyNameMap);
            }
            map.put("classifyNameList", classifyNameResultList);

            QueryNewAppClassifyNameDTO queryNewAppClassifyNameDTO = new QueryNewAppClassifyNameDTO();
            queryNewAppClassifyNameDTO.setCustomerId(request.getCustomerId());
            queryNewAppClassifyNameDTO.setAppIds(Arrays.asList(Long.parseLong(appDetailsVO.getFileUniqueId())));
            List<NewAppClassifyNameVO> newAppClassifyList = customerNewAppClassifyRelationMapper
                    .selectAppListByAppIds(queryNewAppClassifyNameDTO);
            List<Map<String, Object>> newAppClassifyResultList = new ArrayList<>();
            for (NewAppClassifyNameVO newAppClassify : newAppClassifyList) {
                Map<String, Object> newAppClassifyMap = new HashMap<>();
                newAppClassifyMap.put("classifyName", newAppClassify.getNewAppClassifyName());
                newAppClassifyMap.put("classifyId", newAppClassify.getNewAppClassifyId());
                newAppClassifyResultList.add(newAppClassifyMap);
            }
            map.put("newAppClassifyList", newAppClassifyResultList);
            result.add(map);
        }
        page = new Page<>(result);
        page.updatePageInfo(appDetailsVOs);
        return page;
    }

    private void callbackCustomer(long customerId, FileUploadTask fileUploadTask, UserFile userFile) {
        Supplier<Object> callbackData = () -> {
            FileDownloadCallbackVO callbackVO = new FileDownloadCallbackVO();
            callbackVO.setTaskId(fileUploadTask.getTaskId().intValue());
            if (userFile != null) {
                callbackVO.setFileUniqueId(userFile.getFileUniqueId());
            }
            callbackVO.setTaskStatus(fileUploadTask.getStatus());
            callbackVO.setOriginFileUrl(fileUploadTask.getOriginalUrl());
            callbackVO.setTaskBusinessType(TaskTypeConstants.FILE_UPLOAD.getType());
            return callbackVO;
        };
        log.info("Callback customer data: customerId={}, callbackData={}", customerId, callbackData.get());
        customerCallbackManager.callback(customerId, CallbackTypeConstants.FILE_UPLOAD_TASK_CALLBACK_TYPE, callbackData);
    }

    private void finishFileUploadTask(FileUploadTask fileUploadTask, int status, UserFile userFile, FileStorage fileStorage) {
        fileUploadTask.setStatus(status);
        fileUploadTask.setUpdatedTime(new Date());
        fileUploadTask.setEndTime(new Date());

        if (userFile != null) { 
            fileUploadTask.setFileStorageId(userFile.getFileStorageId());
            if (fileStorage != null) {
                fileUploadTask.setFileSize(fileStorage.getFileSize());
            }
            fileUploadTaskMapper.updateById(fileUploadTask);
        }
        

        callbackTaskManager.updateFileTaskStatus(fileUploadTask);
        callbackCustomer(fileUploadTask.getCustomerId(), fileUploadTask, userFile);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> aliyunfcCallback1(String requestBody) throws Exception {
        log.info("Aliyun FC callback content: {}", requestBody);


        JSONObject jsonObject = JSON.parseObject(requestBody);

        int uploadTaskId = jsonObject.getIntValue("uploadTaskId");
        FileUploadTask fileUploadTask = fileUploadTaskMapper.selectById(uploadTaskId);
        if (fileUploadTask == null) {
            log.warn("Upload task does not exist: {}", uploadTaskId);
            return Result.fail(500, "Upload task does not exist");
        }

        if (!handleUploadSuccess(jsonObject, fileUploadTask)) {
            return Result.fail(500, "Upload task failed");
        }

        return Result.ok();
    }

    private boolean handleUploadSuccess(JSONObject jsonObject, FileUploadTask fileUploadTask) {
        int code = jsonObject.getIntValue("code");
        // 只要有返回code，就认为上传成功
        if (code != 200) {
            fileUploadTask.setErrorMsg(jsonObject.getString("msg"));
            finishFileUploadTask(fileUploadTask, TaskStatusConstants.FAIL_ALL.getStatus(), null, null);
            return true;
        }

        String fileMd5 = jsonObject.getString("fileMd5");
        Long fileSize = jsonObject.getLongValue("fileSize");
        String ossPath = jsonObject.getString("ossPath");
        String privateUrl = ossService.getPrivateUrl(ossPath);
        String publicUrl = ossService.getPublicUrl(ossPath);
        

        UserFile userFile = this.baseMapper.selectById(fileUploadTask.getFileId());
        // 实际上是因为上传成功，但是用户文件不存在，返回false, 这样阿里云fc会重试
        if (userFile == null) {
            handleUserFileNotFound(fileUploadTask);
            return false;
        }
        String fileName = getFilenameInUrl(ossPath);
        String fileExt = getFileExtension(fileName);
        if (null == fileExt) {
            fileExt = "apk";
        }
        FileStorage fileStorage = fileStorageService.findByMd5(fileMd5, null);
        if (fileStorage != null) {
            if (fileStorage.getUploadStatus().equals(UPLOAD_STATUS_SUCCESS)) {
                log.warn("File already exists with MD5: {}", fileMd5);
            } else {
                fileStorage.setUploadStatus(UPLOAD_STATUS_SUCCESS);
                fileStorage.setFileSize(fileSize);
                fileStorage.setStoragePath(ossPath);
                fileStorage.setPrivateUrl(privateUrl);
                fileStorage.setPublicUrl(publicUrl);
                fileStorage.setFileExt(fileExt);
                fileStorage.setFileMd5(fileMd5);
                fileStorageService.saveFileStorage(fileStorage);
            }
        } else {
            fileStorage = new FileStorage();
            fileStorage.setUploadStatus(UPLOAD_STATUS_SUCCESS);
            fileStorage.setFileSize(fileSize);
            fileStorage.setStoragePath(ossPath);
            fileStorage.setPrivateUrl(privateUrl);
            fileStorage.setPublicUrl(publicUrl);
            fileStorage.setFileExt(fileExt);
            fileStorage.setFileMd5(fileMd5);
            fileStorage.setCreatedTime(new Date());
            fileStorageService.saveFileStorage(fileStorage);
        }
        userFile.setFileStorageId(fileStorage.getId());
        userFile.setFileStatus(FILE_STATUS_VALID);
        userFile.setUpdatedTime(new Date());
        userFile.setUpdatedBy(fileUploadTask.getCustomerId());
        this.baseMapper.updateById(userFile);

        
        handleAppInfo(jsonObject, fileStorage.getId());
        finishFileUploadTask(fileUploadTask, TaskStatusConstants.SUCCESS.getStatus(), userFile, fileStorage);

        return true;
    }

    private void handleUserFileNotFound(FileUploadTask fileUploadTask) {
        fileUploadTask.setErrorMsg("User file does not exist: " + fileUploadTask.getFileId());
        finishFileUploadTask(fileUploadTask, TaskStatusConstants.FAIL_ALL.getStatus(), null, null);
        log.warn("User file does not exist: {}", fileUploadTask.getFileId());
    }


    private void handleAppInfo(JSONObject jsonObject, Long fileStorageId) {
        if (jsonObject.containsKey("appInfo") && jsonObject.getJSONObject("appInfo") != null) {
            appParseService.updateAppFile(fileStorageId, jsonObject.getJSONObject("appInfo"));
        } else if (jsonObject.containsKey("parseErrorLog") && StringUtils.hasText(jsonObject.getString("parseErrorLog"))) {
            appParseService.recordParseLog(fileStorageId, "failed", null, jsonObject.getString("parseErrorLog"));
        }
    }

    private Integer tryGenerateAppId(Long customerId) {
        // 需要随机一个appId， 并且保证唯一
        // 生成随机的int类型appId并确保唯一
        int randomAppId = (int) (Math.random() * Integer.MAX_VALUE);
        // 检查是否已存在
        // 最多尝试10次生成唯一ID
        int attempts = 0;
        while (this.baseMapper.selectOne(new LambdaQueryWrapper<UserFile>()
                .eq(UserFile::getCustomerId, customerId)
                .eq(UserFile::getFileUniqueId, String.valueOf(randomAppId))) != null) {
            randomAppId = (int) (Math.random() * Integer.MAX_VALUE);
            attempts++;
            if (attempts >= MAX_APP_ID_GENERATION_ATTEMPTS) {
                log.error("Failed to generate unique app ID after {} attempts for customer: {}",
                        MAX_APP_ID_GENERATION_ATTEMPTS, customerId);
                throw new RuntimeException("Failed to generate unique app ID");
            }
        }
        return randomAppId;
    }
}